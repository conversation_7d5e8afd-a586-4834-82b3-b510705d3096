@echo off
echo Setting up Windows Task Scheduler for SqlQueryLogger
echo.

REM Get current directory
set CURRENT_DIR=%~dp0
set EXE_PATH=%CURRENT_DIR%bin\Release\SqlQueryLogger.exe
set WORKING_DIR=%CURRENT_DIR%bin\Release

echo Executable path: %EXE_PATH%
echo Working directory: %WORKING_DIR%
echo.

REM Check if executable exists
if not exist "%EXE_PATH%" (
    echo ERROR: SqlQueryLogger.exe not found!
    echo Please build the application first using build.bat
    pause
    exit /b 1
)

echo Creating scheduled task...
echo.

REM Create a scheduled task to run every hour
schtasks /create /tn "SqlQueryLogger-Hourly" /tr "\"%EXE_PATH%\"" /sc hourly /st 09:00 /sd %date% /ru "SYSTEM" /f

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ Scheduled task created successfully!
    echo   Task Name: SqlQueryLogger-Hourly
    echo   Schedule: Every hour starting at 9:00 AM
    echo   User: SYSTEM
    echo.
    echo To modify the schedule:
    echo   1. Open Task Scheduler (taskschd.msc)
    echo   2. Find "SqlQueryLogger-Hourly" task
    echo   3. Right-click and select Properties
    echo.
    echo To run the task manually:
    echo   schtasks /run /tn "SqlQueryLogger-Hourly"
    echo.
    echo To delete the task:
    echo   schtasks /delete /tn "SqlQueryLogger-Hourly" /f
    echo.
) else (
    echo.
    echo ✗ Failed to create scheduled task!
    echo You may need to run this script as Administrator.
    echo.
)

pause
