using System;
using System.Data.SqlClient;
using System.Configuration;
using System.IO;
using System.Text;

namespace SqlQueryLogger
{
    class Program
    {
        private static string logDirectory;
        private static bool enableDetailedLogging;

        static void Main(string[] args)
        {
            try
            {
                // Initialize configuration
                InitializeConfiguration();

                // Create log directory if it doesn't exist
                CreateLogDirectory();

                // Get query to execute
                string queryName = ConfigurationManager.AppSettings["QueryName"] ?? "BatchReplyQuery";
                string query = ConfigurationManager.AppSettings[queryName];

                if (string.IsNullOrEmpty(query))
                {
                    LogError("Query '" + queryName + "' not found in configuration.");
                    return;
                }

                // Execute query and log results
                ExecuteQueryAndLog(query, queryName);
            }
            catch (Exception ex)
            {
                LogError("Application error: " + ex.Message);
                if (enableDetailedLogging)
                {
                    LogError("Stack trace: " + ex.StackTrace);
                }
            }
        }

        private static void InitializeConfiguration()
        {
            logDirectory = ConfigurationManager.AppSettings["LogDirectory"] ?? "Logs";
            bool.TryParse(ConfigurationManager.AppSettings["EnableDetailedLogging"], out enableDetailedLogging);
        }

        private static void CreateLogDirectory()
        {
            if (!Directory.Exists(logDirectory))
            {
                Directory.CreateDirectory(logDirectory);
            }
        }

        private static void ExecuteQueryAndLog(string query, string queryName)
        {
            string connectionString = ConfigurationManager.ConnectionStrings["DbConn"].ConnectionString;
            string timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

            try
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    LogInfo("Connecting to database...");
                    connection.Open();
                    LogInfo("Connected successfully. Executing query: " + queryName);

                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        // Set command timeout (30 seconds default)
                        command.CommandTimeout = 30;
                        
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            int recordCount = 0;
                            StringBuilder resultBuilder = new StringBuilder();
                            
                            // Write headers
                            if (reader.HasRows)
                            {
                                for (int i = 0; i < reader.FieldCount; i++)
                                {
                                    resultBuilder.Append(reader.GetName(i));
                                    if (i < reader.FieldCount - 1)
                                        resultBuilder.Append("\t");
                                }
                                resultBuilder.AppendLine();
                                resultBuilder.AppendLine(new string('-', 100));
                            }
                            
                            // Read data
                            while (reader.Read())
                            {
                                recordCount++;
                                for (int i = 0; i < reader.FieldCount; i++)
                                {
                                    object value = reader[i];
                                    resultBuilder.Append(value == DBNull.Value ? "NULL" : value.ToString());
                                    if (i < reader.FieldCount - 1)
                                        resultBuilder.Append("\t");
                                }
                                resultBuilder.AppendLine();
                            }
                            
                            // Log successful execution
                            string successMessage = "[" + timestamp + "] Query '" + queryName + "' executed successfully. Records returned: " + recordCount;
                            LogSuccess(successMessage);

                            if (recordCount > 0)
                            {
                                LogSuccess("Query Results:\n" + resultBuilder.ToString());
                            }
                            else
                            {
                                LogWarning("[" + timestamp + "] Query '" + queryName + "' returned no records.");
                            }
                        }
                    }
                }
            }
            catch (SqlException sqlEx)
            {
                string errorMessage = "[" + timestamp + "] SQL Error executing query '" + queryName + "': " + sqlEx.Message;
                LogError(errorMessage);

                if (enableDetailedLogging)
                {
                    LogError("SQL Error Details - Number: " + sqlEx.Number + ", Severity: " + sqlEx.Class + ", State: " + sqlEx.State);
                    LogError("Stack trace: " + sqlEx.StackTrace);
                }
            }
            catch (Exception ex)
            {
                string errorMessage = "[" + timestamp + "] General error executing query '" + queryName + "': " + ex.Message;
                LogError(errorMessage);

                if (enableDetailedLogging)
                {
                    LogError("Stack trace: " + ex.StackTrace);
                }
            }
        }

        private static void LogSuccess(string message)
        {
            LogToFile("Success", message);
            Console.WriteLine("SUCCESS: " + message);
        }

        private static void LogError(string message)
        {
            LogToFile("Error", message);
            Console.WriteLine("ERROR: " + message);
        }

        private static void LogWarning(string message)
        {
            LogToFile("Warning", message);
            Console.WriteLine("WARNING: " + message);
        }

        private static void LogInfo(string message)
        {
            if (enableDetailedLogging)
            {
                LogToFile("Info", message);
            }
            Console.WriteLine("INFO: " + message);
        }

        private static void LogToFile(string status, string message)
        {
            try
            {
                string fileName = Path.Combine(logDirectory, status + "_" + DateTime.Now.ToString("yyyyMMdd") + ".txt");
                string logEntry = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + " - " + message + Environment.NewLine;
                File.AppendAllText(fileName, logEntry);
            }
            catch (Exception ex)
            {
                Console.WriteLine("Failed to write to log file: " + ex.Message);
            }
        }
    }
}
