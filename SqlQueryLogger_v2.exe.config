<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <connectionStrings>
    <add name="DbConn" connectionString="Data Source=localhost;Initial Catalog=TestDB;Integrated Security=True" />
    <add name="SqliteConn" connectionString="Data Source=test.db;Version=3;" />
  </connectionStrings>

  <appSettings>
    <add key="QueryName" value="SqliteTestQuery" />
    <add key="ConnectionName" value="SqliteConn" />
    <add key="LogDirectory" value="Logs" />
    <add key="EnableDetailedLogging" value="true" />
    <add key="SqliteTestQuery" value="SELECT 'Hello SQLite' as Message, datetime('now') as CurrentTime, 1 as TestNumber" />
    <add key="SqliteVersionQuery" value="SELECT sqlite_version() as SQLiteVersion" />
    <add key="BatchReplyQuery" value="SELECT 1 as TestID, 'Test Client' as Client, GETDATE() as DateCreated" />
  </appSettings>
</configuration>
