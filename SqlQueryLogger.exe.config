<?xml version="1.0" encoding="utf-8" ?>
<configuration>
  <connectionStrings>
    <!-- Replace with your actual SQL Server connection string -->
    <add name="DbConn" connectionString="Data Source=zajhbudw01.bitventure.co.za;Initial Catalog=EasyDebit;User ID=edservices;pwd=*************"/>
    <!--Data Source=YOUR_SERVER_NAME;Initial Catalog=YOUR_DATABASE_NAME;Integrated Security=True"-->
  </connectionStrings>
  
  <appSettings>
    <!-- Query configuration -->
    <add key="QueryName" value="BatchReplyQuery" />
    <add key="LogDirectory" value="Logs" />
    <add key="EnableDetailedLogging" value="true" />
    
    <!-- Batch Reply Query -->
    <add key="BatchReplyQuery" value="select B.typeid, I.Description 'Client', B.ParentBatchID 'BatchID',ISNULL(BO.Name,RB.Name)'File Name', BO.TotalAmount, ISNULL(BO.TotalCount,RB.TotalCount) 'TotalCount',B.InstitutionCodeID 'Inst.code', ISNULL(BO.DateCreated,RB.DateCreated) 'DateCreated', ISNULL(BO.TransmissionNumber,RB.TransmissionNumber) 'Transmission.No', ISNULL(BO.DateModified,RB.DateModified )'Modifieddate' ,B.ID 'ReplyID',B.Name 'ReplyName' from batch B left join Batch BO with (nolock) on B.ParentBatchID = BO.ID left join InstitutionCode I with (nolock) on I.ID = B.InstitutionCodeID left join RejectedBatch RB with (nolock) on RB.ID = B.ParentBatchID left join BatchInstructionPayment BI with (nolock) on BI.BatchID = RB.ID where B.Name like 'Reply%' and B.DateCreated >=convert(date,getdate() -0) And BO.TotalAmount <> 0 union select B.typeid, PC.Description 'Client', B.ParentBatchID 'BatchID',ISNULL(B.Name,'')'File Name', ISNULL(B.TotalAmount,0)'TotalAmount', ISNULL(B.TotalCount,0) 'TotalCount', PC.ID 'InstitutionCodeID', ISNULL(B.DateCreated,Getdate()) 'DateCreated', ISNULL(B.TransmissionNumber,1) 'Transmission.No', ISNULL(B.DateModified,Getdate() )'Modifieddate' ,ReplyID=1,'ReplyName' ='' from batch B (nolock) inner join ProfileCode PC (nolock) on PC.ID = B.ProfileCodeID where B.DateCreated >=convert(date,getdate() -0) order by DateCreated" />
  </appSettings>
</configuration>
