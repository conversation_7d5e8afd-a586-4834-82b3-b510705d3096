# PowerShell build script for SqlQueryLogger
Write-Host "Building SqlQueryLogger for .NET Framework 3.5..." -ForegroundColor Green

# Function to find MSBuild
function Find-MSBuild {
    $msbuildPaths = @(
        "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\MSBuild.exe",
        "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe",
        "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\Enterprise\MSBuild\Current\Bin\MSBuild.exe",
        "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2017\BuildTools\MSBuild\15.0\Bin\MSBuild.exe",
        "${env:ProgramFiles(x86)}\MSBuild\14.0\Bin\MSBuild.exe"
    )
    
    # Check if msbuild is in PATH
    try {
        $null = Get-Command msbuild -ErrorAction Stop
        return "msbuild"
    }
    catch {
        # Search in common locations
        foreach ($path in $msbuildPaths) {
            if (Test-Path $path) {
                return $path
            }
        }
    }
    
    return $null
}

# Find MSBuild
$msbuildPath = Find-MSBuild

if (-not $msbuildPath) {
    Write-Host "ERROR: MSBuild not found!" -ForegroundColor Red
    Write-Host "Please install Visual Studio Build Tools or .NET Framework SDK." -ForegroundColor Yellow
    Write-Host "Download from: https://visualstudio.microsoft.com/downloads/#build-tools-for-visual-studio-2019" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Using MSBuild: $msbuildPath" -ForegroundColor Cyan

# Build the project
Write-Host "Building Release configuration..." -ForegroundColor Yellow
& $msbuildPath "SqlQueryLogger.csproj" /p:Configuration=Release /p:Platform="Any CPU" /verbosity:minimal

if ($LASTEXITCODE -eq 0) {
    Write-Host "`nBuild successful!" -ForegroundColor Green
    Write-Host "Executable created at: bin\Release\SqlQueryLogger.exe" -ForegroundColor Green
    
    # Copy config file
    if (Test-Path "App.config") {
        Copy-Item "App.config" "bin\Release\SqlQueryLogger.exe.config" -Force
        Write-Host "Configuration file copied to output directory." -ForegroundColor Green
    }
    
    Write-Host "`nTo run the application:" -ForegroundColor Cyan
    Write-Host "  cd bin\Release" -ForegroundColor White
    Write-Host "  .\SqlQueryLogger.exe" -ForegroundColor White
}
else {
    Write-Host "`nBuild failed! Check the error messages above." -ForegroundColor Red
}

Read-Host "`nPress Enter to exit"
