# SQL Query Logger - .NET Framework 3.5

A Windows console application that connects to SQL Server, executes queries, and logs results to different text files based on execution status.

## Features

- ✅ .NET Framework 3.5 compatible
- ✅ Configurable SQL queries via App.config
- ✅ Separate log files for Success, Error, Warning, and Info
- ✅ Daily log file rotation
- ✅ Detailed error logging with SQL error codes
- ✅ Scheduled execution support via Windows Task Scheduler

## Prerequisites

1. **Windows with .NET Framework 3.5** installed
2. **MSBuild or Visual Studio Build Tools** for compilation
3. **SQL Server** instance (local or remote)
4. **Visual Studio Code** (for development)

## Setup Instructions

### 1. Configure Database Connection

Edit `App.config` and update the connection string:

```xml
<add name="DbConn" connectionString="Data Source=YOUR_SERVER_NAME;Initial Catalog=YOUR_DATABASE_NAME;Integrated Security=True" />
```

For SQL Server Authentication, use:
```xml
<add name="DbConn" connectionString="Data Source=YOUR_SERVER_NAME;Initial Catalog=YOUR_DATABASE_NAME;User ID=username;Password=password" />
```

### 2. Build the Application

**Option A: Using Batch Script**
```cmd
build.bat
```

**Option B: Using PowerShell**
```powershell
.\build.ps1
```

**Option C: Manual MSBuild**
```cmd
msbuild SqlQueryLogger.csproj /p:Configuration=Release
```

### 3. Run the Application

```cmd
cd bin\Release
SqlQueryLogger.exe
```

## Configuration

### App.config Settings

- **QueryName**: Which query to execute (default: "BatchReplyQuery")
- **LogDirectory**: Where to store log files (default: "Logs")
- **EnableDetailedLogging**: Include detailed error information (default: true)

### Adding New Queries

Add new queries to the `<appSettings>` section:

```xml
<add key="MyCustomQuery" value="SELECT * FROM MyTable WHERE Date >= GETDATE()" />
```

Then set the QueryName to execute it:
```xml
<add key="QueryName" value="MyCustomQuery" />
```

## Log Files

The application creates separate log files in the `Logs` directory:

- **Success_YYYYMMDD.txt**: Successful query executions and results
- **Error_YYYYMMDD.txt**: SQL errors and connection issues
- **Warning_YYYYMMDD.txt**: Warnings (e.g., no records returned)
- **Info_YYYYMMDD.txt**: Detailed execution information (if enabled)

## Scheduling with Task Scheduler

1. Open **Task Scheduler** (taskschd.msc)
2. Create Basic Task
3. Set trigger (daily, hourly, etc.)
4. Action: **Start a program**
5. Program: `C:\path\to\SqlQueryLogger.exe`
6. Start in: `C:\path\to\bin\Release\`

## Troubleshooting

### Build Issues

1. **MSBuild not found**: Install Visual Studio Build Tools
2. **.NET Framework 3.5 missing**: Enable in Windows Features
3. **Permission errors**: Run as Administrator

### Runtime Issues

1. **Connection failed**: Check connection string and SQL Server accessibility
2. **Config file not found**: Ensure `SqlQueryLogger.exe.config` exists next to the .exe
3. **Query timeout**: Increase CommandTimeout in Program.cs

## Current Query

The application is pre-configured with your batch reply query that:
- Joins batch, institution, and profile tables
- Filters for reply batches created today
- Returns client info, batch details, and transmission data
- Excludes zero-amount transactions

## Development

To modify the application:

1. Edit `Program.cs` for logic changes
2. Edit `App.config` for configuration
3. Rebuild using `build.bat` or `build.ps1`
4. Test the new executable

## Support

For issues or modifications, check:
- Log files for detailed error information
- SQL Server connectivity
- .NET Framework 3.5 installation
- File permissions in the application directory
