@echo off
echo Building SqlQueryLogger for .NET Framework 3.5...
echo.

REM Check if MSBuild is available
where msbuild >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo MSBuild not found in PATH. Trying common locations...
    
    REM Try Visual Studio 2019 Build Tools
    if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\MSBuild.exe" (
        set MSBUILD_PATH="C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\MSBuild.exe"
        goto :build
    )
    
    REM Try Visual Studio 2017 Build Tools
    if exist "C:\Program Files (x86)\Microsoft Visual Studio\2017\BuildTools\MSBuild\15.0\Bin\MSBuild.exe" (
        set MSBUILD_PATH="C:\Program Files (x86)\Microsoft Visual Studio\2017\BuildTools\MSBuild\15.0\Bin\MSBuild.exe"
        goto :build
    )
    
    REM Try .NET Framework MSBuild
    if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" (
        set MSBUILD_PATH="C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe"
        goto :build
    )
    
    REM Try Windows SDK
    if exist "C:\Program Files (x86)\MSBuild\14.0\Bin\MSBuild.exe" (
        set MSBUILD_PATH="C:\Program Files (x86)\MSBuild\14.0\Bin\MSBuild.exe"
        goto :build
    )
    
    echo ERROR: MSBuild not found. Please install Visual Studio Build Tools or .NET Framework SDK.
    echo You can download it from: https://visualstudio.microsoft.com/downloads/#build-tools-for-visual-studio-2019
    pause
    exit /b 1
) else (
    set MSBUILD_PATH=msbuild
)

:build
echo Using MSBuild: %MSBUILD_PATH%
echo.

REM Build Release version
echo Building Release configuration...
%MSBUILD_PATH% SqlQueryLogger.csproj /p:Configuration=Release /p:Platform="Any CPU" /verbosity:minimal

if %ERRORLEVEL% EQU 0 (
    echo.
    echo Build successful!
    echo Executable created at: bin\Release\SqlQueryLogger.exe
    echo.
    
    REM Copy config file to output directory
    if exist "App.config" (
        copy "App.config" "bin\Release\SqlQueryLogger.exe.config" >nul
        echo Configuration file copied to output directory.
    )
    
    echo.
    echo To run the application:
    echo   cd bin\Release
    echo   SqlQueryLogger.exe
    echo.
) else (
    echo.
    echo Build failed! Check the error messages above.
    echo.
)

pause
